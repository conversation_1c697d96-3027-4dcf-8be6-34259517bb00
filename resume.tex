\documentclass[11pt,a4paper,sans]{moderncv}

% 现代简历样式
\moderncvstyle{casual}
\moderncvcolor{blue}

% 字符编码和字体
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{CJKutf8}
\usepackage{xcolor}
\usepackage{tikz}

% 页面设置
\usepackage[scale=0.88]{geometry}
\setlength{\hintscolumnwidth}{3.2cm}

% 自定义颜色
\definecolor{primarycolor}{RGB}{0,102,204}
\definecolor{accentcolor}{RGB}{51,153,255}
\definecolor{textcolor}{RGB}{64,64,64}

% 个人信息
\name{田}{永恒}
\title{\textcolor{primarycolor}{\textbf{人工智能专业本科生 | 算法竞赛选手}}}
\phone[mobile]{电话号码}
\email{邮箱地址}
\social[github]{huxint}
\extrainfo{江西师范大学}

% 自定义命令
\newcommand{\cvdoublecolumn}[2]{%
\cvitem[0.75em]{}{%
\begin{minipage}[t]{\listdoubleitemcolumnwidth}#1\end{minipage}%
\hfill%
\begin{minipage}[t]{\listdoubleitemcolumnwidth}#2\end{minipage}%
}%
}

% 自定义项目条目
\newcommand{\cvproject}[6]{%
\cventry{#1}{#2}{#3}{#4}{#5}{#6}
}

% 自定义技能条目
\newcommand{\cvskillitem}[2]{%
\cvitem{#1}{\textcolor{primarycolor}{#2}}
}

% 自定义奖项条目
\newcommand{\cvaward}[3]{%
\cvitem{#1}{\textbf{\textcolor{accentcolor}{#2}} \hfill #3}
}

\begin{document}
\begin{CJK}{UTF8}{gbsn}

\makecvtitle

% 个人简介
\section{个人简介}
\cvitem{}{
\textcolor{textcolor}{
热爱算法竞赛与人工智能的本科生，具备扎实的编程基础和丰富的项目经验。
在多项国家级竞赛中获奖，擅长C++和Python开发，对机器学习和深度学习有深入理解。
具备良好的团队协作能力和项目管理经验。
}
}

% 教育背景
\section{教育背景}
\cventry{2023.01--2027.01}{\textbf{人工智能专业 · 本科}}{江西师范大学}{\textcolor{primarycolor}{在读}}{}{
\begin{itemize}
\item \textbf{核心课程：}机器学习、深度学习、数据结构与算法、计算机视觉、自然语言处理
\item \textbf{专业排名：}前20\%（预估）
\item \textbf{GPA：}3.8/4.0（预估）
\end{itemize}
}

% 竞赛获奖
\section{竞赛获奖}
\cvaward{2025.07}{团体程序设计天梯赛}{国家二等奖（团体）、国家三等奖（个人）}
\cvaward{2025.06}{ICPC江西省赛}{银牌}
\cvaward{2025.05}{蓝桥杯程序设计竞赛}{省一等奖、国家三等奖}

% 项目经历
\section{项目经历}

\cvproject{2025.01}{\textbf{算法竞赛模板库}}{个人项目}{\textcolor{primarycolor}{C++}}{GitHub: huxint/CompetitiveProgramming}{
\begin{itemize}
\item \textbf{核心功能：}设计并实现涵盖图论、动态规划、数据结构等核心算法的模板库
\item \textbf{技术亮点：}利用C++模板特性构建通用接口，提高代码复用性和可维护性
\item \textbf{项目规模：}包含30+经典算法实现，支持快速调用和定制化修改
\item \textbf{应用价值：}为算法竞赛提供高效的代码框架，显著提升编程效率
\end{itemize}
}

\cvproject{2025.01}{\textbf{实时手写数字识别系统}}{深度学习项目}{\textcolor{primarycolor}{Python, TensorFlow/PyTorch}}{深度学习项目}{
\begin{itemize}
\item \textbf{模型架构：}基于卷积神经网络(CNN)设计三层卷积架构(32-64-128通道)
\item \textbf{优化策略：}实现全连接层配合Dropout正则化，有效防止过拟合
\item \textbf{性能表现：}采用高容量网络设计，在MNIST数据集上达到\textcolor{accentcolor}{\textbf{98\%+}}识别准确率
\item \textbf{实时应用：}集成实时图像处理模块，支持摄像头输入的实时数字识别
\end{itemize}
}

\cvproject{2025.01}{\textbf{实时人脸表情识别系统}}{计算机视觉项目}{\textcolor{primarycolor}{Python, OpenCV}}{计算机视觉项目}{
\begin{itemize}
\item \textbf{数据集：}基于Kaggle FER2013表情数据集训练深度学习模型
\item \textbf{协作开发：}与AI Agent协作完成端到端的表情识别pipeline
\item \textbf{功能实现：}实现\textcolor{accentcolor}{\textbf{7种}}基本表情的实时检测与分类
\item \textbf{系统集成：}集成人脸检测与表情分析，构建完整的视觉感知系统
\end{itemize}
}

\cvproject{2025.01}{\textbf{Zillow房价预测项目}}{机器学习竞赛}{\textcolor{primarycolor}{Python, Scikit-learn}}{Kaggle竞赛项目}{
\begin{itemize}
\item \textbf{竞赛参与：}参与Kaggle "Zillow Prize: Home Value Prediction"竞赛
\item \textbf{数据处理：}进行大规模房地产数据的特征工程和数据预处理
\item \textbf{模型构建：}构建集成学习模型，融合多种回归算法提升预测精度
\item \textbf{业务洞察：}深入分析房价影响因素，提供数据驱动的市场洞察
\end{itemize}
}

\cvproject{2025.01}{\textbf{微信小程序开发}}{全栈开发项目}{\textcolor{primarycolor}{JavaScript, 微信开发者工具}}{移动开发项目}{
\begin{itemize}
\item \textbf{独立开发：}独立完成小程序的前端界面设计与后端逻辑开发
\item \textbf{功能实现：}实现用户交互、数据存储、API调用等核心功能
\item \textbf{用户体验：}遵循微信小程序开发规范，确保良好的用户体验
\item \textbf{完整流程：}完成从需求分析到上线部署的完整开发流程
\end{itemize}
}

\cvproject{2023.10}{\textbf{音乐社区软件}}{团队项目负责人}{\textcolor{primarycolor}{多技术栈}}{社区平台项目}{
\begin{itemize}
\item \textbf{项目管理：}担任项目主要负责人，协调团队开发进度和技术方案
\item \textbf{架构设计：}负责核心功能模块的架构设计和代码实现
\item \textbf{功能开发：}实现用户社交、音乐分享、评论互动等社区功能
\item \textbf{质量保证：}管理项目版本控制和代码质量，确保项目按时交付
\end{itemize}
}

% 技能专长
\section{技能专长}

\cvdoublecolumn{
\textbf{\textcolor{primarycolor}{编程语言}}
\begin{itemize}
\item \textcolor{accentcolor}{\textbf{C++}} (熟练) \textcolor{gray}{$\bullet\bullet\bullet\bullet\bullet$}
\item \textcolor{accentcolor}{\textbf{Python}} (熟练) \textcolor{gray}{$\bullet\bullet\bullet\bullet\bullet$}
\item \textcolor{accentcolor}{\textbf{JavaScript}} (良好) \textcolor{gray}{$\bullet\bullet\bullet\circ\circ$}
\end{itemize}
}{
\textbf{\textcolor{primarycolor}{技术框架}}
\begin{itemize}
\item TensorFlow/PyTorch
\item OpenCV
\item Scikit-learn
\item Git/GitHub
\end{itemize}
}

\cvdoublecolumn{
\textbf{\textcolor{primarycolor}{算法竞赛}}
\begin{itemize}
\item 数据结构与算法
\item 图论算法
\item 动态规划
\item 数学建模
\end{itemize}
}{
\textbf{\textcolor{primarycolor}{人工智能}}
\begin{itemize}
\item 机器学习
\item 深度学习
\item 计算机视觉
\item 数据分析
\end{itemize}
}

% 校园经历
\section{校园经历}
\cventry{2025.07--至今}{\textbf{学干}}{计算机信息科学协会}{\textcolor{primarycolor}{学生组织}}{}{
\begin{itemize}
\item \textbf{活动组织：}组织技术分享会和编程竞赛培训活动
\item \textbf{学术指导：}协助新生适应专业学习，提供学术指导
\item \textbf{团队协作：}参与协会日常管理和活动策划工作
\end{itemize}
}

% 求职意向
\section{求职意向}
\cvskillitem{期望职位}{C++开发工程师 / 算法工程师 / 人工智能工程师}
\cvskillitem{工作地点}{不限（优先考虑一线城市）}
\cvskillitem{期望薪资}{面议}
\cvskillitem{到岗时间}{2027年7月（可实习）}

\end{CJK}
\end{document}
