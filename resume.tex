\documentclass[11pt,a4paper,sans]{moderncv}

% 现代简历样式
\moderncvstyle{banking}
\moderncvcolor{blue}

% 字符编码
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{CJKutf8}

% 页面设置
\usepackage[scale=0.85]{geometry}
\setlength{\hintscolumnwidth}{2.8cm}

% 个人信息
\name{田}{永恒}
\title{人工智能专业本科生}
\phone[mobile]{电话号码}
\email{邮箱地址}
\social[github]{huxint}

% 自定义命令
\newcommand{\cvdoublecolumn}[2]{%
\cvitem[0.75em]{}{%
\begin{minipage}[t]{\listdoubleitemcolumnwidth}#1\end{minipage}%
\hfill%
\begin{minipage}[t]{\listdoubleitemcolumnwidth}#2\end{minipage}%
}%
}

\begin{document}
\begin{CJK}{UTF8}{gbsn}

\makecvtitle

% 教育背景
\section{教育背景}
\cventry{2023.01--2027.01}{人工智能专业 · 本科}{江西师范大学}{}{}{
\begin{itemize}
\item 主修课程：机器学习、深度学习、数据结构与算法、计算机视觉、自然语言处理
\item 专业排名：前20\%（预估）
\end{itemize}
}

% 竞赛获奖
\section{竞赛获奖}
\cvitem{2025.07}{团体程序设计天梯赛 \textbf{国家二等奖}（团体）、\textbf{国家三等奖}（个人）}
\cvitem{2025.06}{ICPC江西省赛 \textbf{银牌}}
\cvitem{2025.05}{蓝桥杯程序设计竞赛 \textbf{省一等奖}、\textbf{国家三等奖}}

% 项目经历
\section{项目经历}

\cventry{2025.01}{算法竞赛模板库}{个人项目}{C++}{GitHub: huxint/CompetitiveProgramming}{
\begin{itemize}
\item 设计并实现了涵盖图论、动态规划、数据结构等核心算法的模板库
\item 利用C++模板特性构建通用接口，提高代码复用性和可维护性
\item 包含30+经典算法实现，支持快速调用和定制化修改
\item 为算法竞赛提供高效的代码框架，显著提升编程效率
\end{itemize}
}

\cventry{2025.01}{实时手写数字识别系统}{深度学习项目}{Python, TensorFlow/PyTorch}{}{
\begin{itemize}
\item 基于卷积神经网络(CNN)设计三层卷积架构(32-64-128通道)
\item 实现全连接层配合Dropout正则化，有效防止过拟合
\item 采用高容量网络设计，在MNIST数据集上达到98\%+识别准确率
\item 集成实时图像处理模块，支持摄像头输入的实时数字识别
\end{itemize}
}

\cventry{2025.01}{实时人脸表情识别系统}{计算机视觉项目}{Python, OpenCV}{}{
\begin{itemize}
\item 基于Kaggle FER2013表情数据集训练深度学习模型
\item 与AI Agent协作完成端到端的表情识别pipeline
\item 实现7种基本表情的实时检测与分类
\item 集成人脸检测与表情分析，构建完整的视觉感知系统
\end{itemize}
}

\cventry{2025.01}{Zillow房价预测项目}{机器学习竞赛}{Python, Scikit-learn}{Kaggle竞赛项目}{
\begin{itemize}
\item 参与Kaggle "Zillow Prize: Home Value Prediction"竞赛
\item 进行大规模房地产数据的特征工程和数据预处理
\item 构建集成学习模型，融合多种回归算法提升预测精度
\item 深入分析房价影响因素，提供数据驱动的市场洞察
\end{itemize}
}

\cventry{2025.01}{微信小程序开发}{全栈开发项目}{JavaScript, 微信开发者工具}{}{
\begin{itemize}
\item 独立完成小程序的前端界面设计与后端逻辑开发
\item 实现用户交互、数据存储、API调用等核心功能
\item 遵循微信小程序开发规范，确保良好的用户体验
\item 完成从需求分析到上线部署的完整开发流程
\end{itemize}
}

\cventry{2023.10}{音乐社区软件}{团队项目负责人}{多技术栈}{}{
\begin{itemize}
\item 担任项目主要负责人，协调团队开发进度和技术方案
\item 负责核心功能模块的架构设计和代码实现
\item 实现用户社交、音乐分享、评论互动等社区功能
\item 管理项目版本控制和代码质量，确保项目按时交付
\end{itemize}
}

% 技能专长
\section{技能专长}
\cvdoublecolumn{
\textbf{编程语言}
\begin{itemize}
\item C++ (熟练)
\item Python (熟练)
\item JavaScript (良好)
\end{itemize}
}{
\textbf{技术框架}
\begin{itemize}
\item TensorFlow/PyTorch
\item OpenCV
\item Scikit-learn
\end{itemize}
}

\cvdoublecolumn{
\textbf{算法竞赛}
\begin{itemize}
\item 数据结构与算法
\item 图论算法
\item 动态规划
\end{itemize}
}{
\textbf{人工智能}
\begin{itemize}
\item 机器学习
\item 深度学习
\item 计算机视觉
\end{itemize}
}

% 校园经历
\section{校园经历}
\cventry{2025.07--至今}{学干}{计算机信息科学协会}{}{}{
\begin{itemize}
\item 组织技术分享会和编程竞赛培训活动
\item 协助新生适应专业学习，提供学术指导
\end{itemize}
}

% 求职意向
\section{求职意向}
\cvitem{期望职位}{C++开发工程师 / 算法工程师 / 人工智能工程师}
\cvitem{工作地点}{不限}
\cvitem{期望薪资}{面议}

\end{CJK}
\end{document}
