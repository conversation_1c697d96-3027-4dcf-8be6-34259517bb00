# 📦 LaTeX 安装指导

## 🚨 错误解决

如果您遇到以下错误：
- `Command \cvskill already defined`
- `Missing number, treated as zero`
- `xelatex 无法识别为命令`

请按照以下步骤解决：

## 🛠️ LaTeX 安装

### Windows 系统

#### 方法1：安装 MiKTeX（推荐）
1. 访问 [MiKTeX官网](https://miktex.org/download)
2. 下载并安装 MiKTeX
3. 安装完成后重启命令行

#### 方法2：安装 TeX Live
1. 访问 [TeX Live官网](https://www.tug.org/texlive/)
2. 下载完整版本（约4GB）
3. 按照安装向导完成安装

### 验证安装
打开命令行，输入：
```bash
xelatex --version
```

如果显示版本信息，说明安装成功。

## 📝 简历文件说明

我为您提供了两个版本：

### 1. resume.tex（完整版）
- 包含所有美化特性
- 需要完整的LaTeX环境
- 可能需要额外的包

### 2. resume_simple.tex（简化版）
- 移除了可能有问题的包
- 更好的兼容性
- 推荐首次使用

## 🚀 编译方法

### 使用简化版本（推荐）
```bash
xelatex resume_simple.tex
```

### 使用完整版本
```bash
xelatex resume.tex
```

## 🔧 常见问题解决

### 1. 缺少中文字体
如果编译时提示缺少中文字体，请：
- Windows: 确保系统有宋体、黑体等中文字体
- Linux: 安装中文字体包 `sudo apt-get install fonts-wqy-zenhei`

### 2. 缺少包文件
如果提示缺少某个包，MiKTeX会自动下载，TeX Live需要手动安装：
```bash
tlmgr install [包名]
```

### 3. 编译错误
如果遇到编译错误：
1. 首先尝试使用 `resume_simple.tex`
2. 检查LaTeX安装是否完整
3. 确保文件编码为UTF-8

## 📱 在线编译（备选方案）

如果本地安装有困难，可以使用在线LaTeX编辑器：

### Overleaf（推荐）
1. 访问 [Overleaf](https://www.overleaf.com/)
2. 注册免费账户
3. 创建新项目
4. 上传 `resume_simple.tex` 文件
5. 在线编译生成PDF

### 其他在线编辑器
- [LaTeX Base](https://latexbase.com/)
- [ShareLaTeX](https://www.sharelatex.com/)

## 🎯 推荐流程

1. **首次使用**：使用 `resume_simple.tex` + Overleaf在线编译
2. **本地环境**：安装MiKTeX后使用 `resume.tex`
3. **自定义修改**：根据需要调整内容和样式

## 💡 修改建议

在编译成功后，您可以：
1. 修改个人信息（姓名、电话、邮箱）
2. 调整项目描述
3. 更新技能列表
4. 添加新的经历

记住：先确保能成功编译，再进行个性化修改！
