@echo off
echo ========================================
echo           LaTeX 简历编译工具
echo ========================================

echo 1. 检查头像文件...
if not exist avatar.jpg (
    echo 未找到头像文件，正在创建示例头像...
    python create_avatar.py
    if errorlevel 1 (
        echo 警告：无法创建示例头像，请手动添加 avatar.jpg 文件
    )
) else (
    echo 头像文件已存在：avatar.jpg
)

echo.
echo 2. 开始编译LaTeX文件...
xelatex resume.tex

echo.
echo 3. 第二次编译（确保引用正确）...
xelatex resume.tex

echo.
echo ========================================
echo 编译完成！生成的PDF文件：resume.pdf
echo ========================================
echo.
echo 提示：
echo - 如需使用自己的头像，请将照片重命名为 avatar.jpg
echo - 建议头像为正方形，分辨率不低于 300x300 像素
echo - 记得修改简历中的个人信息（电话、邮箱等）
echo.
pause
