@echo off
echo ========================================
echo           LaTeX 简历编译工具
echo ========================================
echo.
echo 正在编译LaTeX简历...
echo.

REM 第一次编译
echo [1/2] 第一次编译...
xelatex -interaction=nonstopmode resume.tex

REM 第二次编译（确保引用正确）
echo [2/2] 第二次编译...
xelatex -interaction=nonstopmode resume.tex

echo.
echo ========================================
echo 编译完成！PDF文件已生成：resume.pdf
echo ========================================
echo.

REM 清理临时文件
echo 清理临时文件...
del *.aux *.log *.out *.fdb_latexmk *.fls *.synctex.gz 2>nul

echo 完成！
pause
