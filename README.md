# 🎯 现代化 LaTeX 简历模板

## 📁 文件说明
- `resume.tex` - 主要的LaTeX简历文件（已优化布局和视觉效果）
- `compile.bat` - 智能编译脚本（自动处理头像和编译）
- `create_avatar.py` - 头像生成工具
- `avatar.jpg` - 个人头像文件
- `README.md` - 详细使用说明

## 🛠️ 编译要求
需要安装以下软件：
- **LaTeX发行版**：MiKTeX 或 TeX Live
- **Python 3.x**（用于生成示例头像）
- **必需包**：CJK中文支持、fontawesome、tikz、xcolor

## 🚀 快速开始

### 本地编译
1. **准备头像**：将您的头像照片重命名为 `avatar.jpg`（推荐300x300像素正方形）
2. **一键编译**：双击运行 `compile.bat`
3. **查看结果**：生成的 `resume.pdf` 即为最终简历

### Overleaf在线编译 ⭐ 推荐
1. **上传文件**：将 `resume_overleaf.tex` 上传到Overleaf
2. **设置编译器**：选择 XeLaTeX 编译器
3. **直接编译**：点击编译按钮即可生成PDF
4. **无需头像**：Overleaf版本已优化，无需额外文件

## ⚙️ 手动编译
```bash
# 本地编译（如果没有头像文件，先生成示例头像）
python create_avatar.py

# 编译LaTeX（需要编译两次确保引用正确）
xelatex resume.tex
xelatex resume.tex

# Overleaf编译（直接使用）
# 上传 resume_overleaf.tex 到 Overleaf
# 选择 XeLaTeX 编译器
# 点击编译
```

## 🎨 设计特色

### 视觉优化
- ✅ **现代化布局**：使用casual风格，更加亲和
- ✅ **个人头像**：支持圆形头像显示
- ✅ **图标装饰**：FontAwesome图标美化各个章节
- ✅ **颜色主题**：专业蓝色配色方案
- ✅ **技能可视化**：技能条和标签展示

### 内容突出
- 🎯 **核心竞争力**：顶部突出显示关键技能标签
- 🏆 **竞赛成就**：醒目的奖项展示
- 💼 **项目亮点**：量化的项目成果描述
- 📊 **技能评级**：可视化的技能熟练度展示

## 📝 自定义指南

### 必须修改的内容
```latex
% 在 resume.tex 中修改以下信息：
\phone[mobile]{您的电话号码}
\email{您的邮箱地址}
\social[github]{您的GitHub用户名}
```

### 可选优化内容
- **个人简介**：修改个人描述
- **项目经历**：更新项目详情和成果
- **技能评级**：调整技能熟练度显示
- **竞赛获奖**：添加或修改获奖信息

## 🎯 适用场景
- **技术岗位申请**：C++/Python开发工程师
- **算法工程师**：突出算法竞赛和项目经验
- **AI/ML岗位**：强调机器学习和深度学习项目
- **实习申请**：适合在校生和应届毕业生

## 💡 使用技巧
1. **头像建议**：使用专业照片，正方形裁剪，清晰度高
2. **内容精简**：每个项目突出2-3个核心亮点
3. **量化成果**：尽量用数字说明项目效果
4. **关键词优化**：根据目标职位调整技能标签

## 🔧 故障排除

### Overleaf常见问题
- **FontAwesome错误**：使用 `resume_overleaf.tex` 版本，已解决兼容性问题
- **编译器选择**：必须选择 XeLaTeX，不要使用 pdfLaTeX
- **中文显示**：Overleaf默认支持CJK，无需额外配置
- **PDF书签警告**：可以忽略，不影响最终效果

### 本地编译问题
- **编译错误**：确保安装了所有必需的LaTeX包
- **中文显示问题**：检查CJK包是否正确安装
- **头像不显示**：确保avatar.jpg文件存在且格式正确
- **图标不显示**：安装fontawesome包或使用Overleaf版本

## 📋 版本说明
- `resume.tex` - 本地编译版本（支持头像，需要FontAwesome）
- `resume_overleaf.tex` - Overleaf优化版本（无需额外文件，兼容性更好）
