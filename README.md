# 🎨 现代化 LaTeX 简历模板

一份专为技术岗位设计的现代化简历模板，具有优雅的视觉效果和专业的排版。

## 📁 文件说明
- `resume.tex` - 主要的LaTeX简历文件
- `compile.bat` - Windows编译脚本
- `compile.sh` - Linux/Mac编译脚本
- `README.md` - 使用说明

## 🛠️ 编译要求
需要安装LaTeX发行版，推荐：
- **Windows**: MiKTeX 或 TeX Live
- **Linux**: TeX Live
- **Mac**: MacTeX

**必需包**：
- CJK中文支持包
- fontawesome5（图标支持）
- xcolor（颜色支持）
- tikz（图形支持）

## 🚀 编译方法

### Windows
```bash
# 方法1：双击运行
compile.bat

# 方法2：命令行
xelatex resume.tex
```

### Linux/Mac
```bash
# 添加执行权限
chmod +x compile.sh

# 运行编译脚本
./compile.sh

# 或直接编译
xelatex resume.tex
```

## ✏️ 自定义修改
在 `resume.tex` 文件中修改以下内容：
- **个人信息**：姓名、电话、邮箱、GitHub等
- **项目经历**：项目描述、技术栈、成果
- **技能专长**：编程语言、框架、工具
- **教育背景**：学校、专业、成绩
- **竞赛获奖**：比赛名称、获奖等级

## 🎯 简历特色

### 🎨 视觉设计
- ✅ 现代化的casual风格布局
- ✅ 专业的蓝色主题配色
- ✅ FontAwesome图标美化
- ✅ 清晰的分栏和间距设计
- ✅ 技能熟练度可视化

### 📝 内容优化
- ✅ 个人简介突出核心优势
- ✅ 项目经历结构化展示
- ✅ 竞赛获奖醒目标注
- ✅ 技能专长分类清晰
- ✅ 求职意向明确具体

### 🔧 技术特性
- ✅ 完整的中文支持
- ✅ 响应式布局设计
- ✅ 自定义颜色主题
- ✅ 模块化代码结构
- ✅ 易于维护和修改

## 🎨 颜色主题
- **主色调**: `primarycolor` - 专业蓝 (RGB: 0,102,204)
- **强调色**: `accentcolor` - 亮蓝 (RGB: 51,153,255)
- **文本色**: `textcolor` - 深灰 (RGB: 64,64,64)

## 📱 适用场景
- 🎯 技术岗位申请（C++、Python、AI等）
- 🎯 算法工程师职位
- 🎯 实习生申请
- 🎯 校园招聘
- 🎯 社会招聘

## 💡 使用建议
1. **个性化定制**：根据目标职位调整项目描述重点
2. **内容精简**：保持简历在1-2页内
3. **数据量化**：用具体数字展示项目成果
4. **关键词优化**：包含职位相关的技术关键词
5. **定期更新**：及时添加新的项目和技能
