#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建示例头像图片
如果您有自己的头像照片，请将其重命名为 avatar.jpg 并放在同一目录下
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_sample_avatar():
    """创建一个示例头像"""
    # 创建一个圆形头像
    size = 300
    img = Image.new('RGB', (size, size), color='lightblue')
    draw = ImageDraw.Draw(img)
    
    # 绘制圆形背景
    draw.ellipse([20, 20, size-20, size-20], fill='#4A90E2', outline='white', width=5)
    
    # 添加文字
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 80)
    except:
        # 如果没有找到字体，使用默认字体
        font = ImageFont.load_default()
    
    # 绘制姓名首字母
    text = "田永恒"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size - text_width) // 2
    y = (size - text_height) // 2
    
    draw.text((x, y), text, fill='white', font=font)
    
    # 保存图片
    img.save('avatar.jpg', 'JPEG', quality=95)
    print("示例头像已创建：avatar.jpg")
    print("建议：请用您的真实头像照片替换此文件以获得更好效果")

if __name__ == "__main__":
    if not os.path.exists('avatar.jpg'):
        create_sample_avatar()
    else:
        print("头像文件 avatar.jpg 已存在")
