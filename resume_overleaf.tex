\documentclass[11pt,a4paper,sans]{moderncv}

% 现代简历样式
\moderncvstyle{casual}
\moderncvcolor{blue}

% 字符编码和包
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{CJKutf8}
\usepackage{xcolor}
\usepackage{tikz}
\usepackage{graphicx}

% 页面设置
\usepackage[scale=0.82,top=1.5cm,bottom=1.5cm]{geometry}
\setlength{\hintscolumnwidth}{3.2cm}

% 自定义颜色
\definecolor{mainblue}{RGB}{0,102,204}
\definecolor{lightblue}{RGB}{230,240,250}
\definecolor{darkgray}{RGB}{64,64,64}

% 个人信息
\name{田}{永恒}
\title{\textcolor{mainblue}{\textbf{人工智能专业本科生 | 算法竞赛选手}}}
\phone[mobile]{电话号码}
\email{邮箱地址}
\social[github]{huxint}
% 注释掉头像，避免文件路径问题
% \photo[80pt][0.4pt]{avatar.jpg}

% 自定义命令
\newcommand{\cvdoublecolumn}[2]{%
\cvitem[0.75em]{}{%
\begin{minipage}[t]{\listdoubleitemcolumnwidth}#1\end{minipage}%
\hfill%
\begin{minipage}[t]{\listdoubleitemcolumnwidth}#2\end{minipage}%
}%
}

% 突出显示的技能标签
\newcommand{\skillbadge}[1]{\colorbox{lightblue}{\textcolor{mainblue}{\textbf{#1}}}}

% 项目亮点标签
\newcommand{\highlight}[1]{\textcolor{mainblue}{\textbf{#1}}}

% 奖项标签
\newcommand{\award}[1]{\textcolor{red}{\textbf{#1}}}

% 简单图标替代
\newcommand{\iconUser}{★}
\newcommand{\iconStar}{✦}
\newcommand{\iconTrophy}{🏆}
\newcommand{\iconGrad}{🎓}
\newcommand{\iconCode}{⚡}
\newcommand{\iconSkill}{⚙}
\newcommand{\iconSchool}{🏫}
\newcommand{\iconTarget}{🎯}

\begin{document}
\begin{CJK}{UTF8}{gbsn}

\makecvtitle

% 个人简介
\section{个人简介}
\cvitem{}{\iconUser \hspace{0.2cm} \large 江西师范大学人工智能专业本科生，具有扎实的算法基础和丰富的项目经验。在多项编程竞赛中获得优异成绩，擅长C++和Python开发，专注于机器学习和深度学习领域。}

% 核心竞争力
\section{核心竞争力}
\cvitem{}{\iconStar \hspace{0.2cm} \skillbadge{算法竞赛} \skillbadge{深度学习} \skillbadge{计算机视觉} \skillbadge{全栈开发} \skillbadge{团队协作}}

% 竞赛获奖 - 突出显示
\section{竞赛获奖}
\cvitem{2025.07}{\iconTrophy \hspace{0.2cm} \award{团体程序设计天梯赛国家二等奖}（团体）、\award{国家三等奖}（个人）}
\cvitem{2025.06}{\iconTrophy \hspace{0.2cm} \award{ICPC江西省赛银牌}}
\cvitem{2025.05}{\iconTrophy \hspace{0.2cm} \award{蓝桥杯程序设计竞赛省一等奖}、\award{国家三等奖}}

% 教育背景
\section{教育背景}
\cventry{2023.01--2027.01}{\highlight{人工智能专业 · 本科}}{江西师范大学}{}{}{
\begin{itemize}
\item \iconGrad \hspace{0.1cm} \highlight{主修课程}：机器学习、深度学习、数据结构与算法、计算机视觉、自然语言处理
\item \iconStar \hspace{0.1cm} \highlight{专业排名}：前20\%（预估）
\item \iconCode \hspace{0.1cm} \highlight{相关技能}：熟练掌握Python、C++，具备扎实的数学基础
\end{itemize}
}

% 项目经历
\section{核心项目经历}

\cventry{2025.01}{\highlight{算法竞赛模板库}}{个人开源项目}{\skillbadge{C++} \skillbadge{算法设计}}{GitHub: huxint/CompetitiveProgramming}{
\begin{itemize}
\item \highlight{核心成果}：设计并实现涵盖图论、动态规划、数据结构等\highlight{30+核心算法}的模板库
\item \highlight{技术亮点}：利用C++模板特性构建通用接口，代码复用性提升\highlight{80\%}
\item \highlight{实际应用}：为算法竞赛提供高效代码框架，编程效率显著提升
\item \highlight{开源贡献}：在GitHub获得多个star，为算法学习者提供参考
\end{itemize}
}

\cventry{2025.01}{\highlight{实时手写数字识别系统}}{深度学习项目}{\skillbadge{Python} \skillbadge{CNN} \skillbadge{计算机视觉}}{}{
\begin{itemize}
\item \highlight{模型架构}：设计三层卷积神经网络(32-64-128通道) + 全连接层 + Dropout
\item \highlight{性能表现}：在MNIST数据集上达到\highlight{98.5\%+识别准确率}
\item \highlight{技术创新}：集成实时图像处理模块，支持摄像头输入的\highlight{毫秒级}实时识别
\item \highlight{工程实现}：完整的端到端系统，从数据预处理到模型部署
\end{itemize}
}

\cventry{2025.01}{\highlight{实时人脸表情识别系统}}{AI视觉项目}{\skillbadge{Python} \skillbadge{OpenCV} \skillbadge{深度学习}}{}{
\begin{itemize}
\item \highlight{数据处理}：基于Kaggle FER2013表情数据集，处理\highlight{35,000+}样本数据
\item \highlight{模型性能}：实现\highlight{7种基本表情}的实时检测，准确率达\highlight{85\%+}
\item \highlight{系统集成}：构建人脸检测→表情分析的完整pipeline，响应时间<100ms
\item \highlight{协作开发}：与AI Agent协作，展现良好的人机协作能力
\end{itemize}
}

\cventry{2025.01}{\highlight{Zillow房价预测项目}}{数据科学竞赛}{\skillbadge{Python} \skillbadge{机器学习} \skillbadge{特征工程}}{Kaggle竞赛}{
\begin{itemize}
\item \highlight{竞赛表现}：参与Kaggle "Zillow Prize"房价预测竞赛，处理\highlight{百万级}房产数据
\item \highlight{特征工程}：设计\highlight{50+}维特征，包括地理位置、房屋属性、市场趋势等
\item \highlight{模型融合}：构建XGBoost+LightGBM+Neural Network集成模型
\item \highlight{业务洞察}：深入分析房价影响因素，提供数据驱动的市场分析报告
\end{itemize}
}

\cventry{2025.01}{\highlight{微信小程序全栈开发}}{移动端项目}{\skillbadge{JavaScript} \skillbadge{小程序} \skillbadge{全栈}}{}{
\begin{itemize}
\item \highlight{独立开发}：单人完成从UI设计到后端API的\highlight{全栈开发}
\item \highlight{技术实现}：实现用户认证、数据存储、实时通信等\highlight{核心功能模块}
\item \highlight{用户体验}：遵循微信设计规范，实现流畅的用户交互体验
\item \highlight{项目管理}：完成需求分析→开发→测试→部署的\highlight{完整SDLC}
\end{itemize}
}

\cventry{2023.10}{\highlight{音乐社区软件}}{团队项目负责人}{\skillbadge{团队管理} \skillbadge{全栈开发}}{}{
\begin{itemize}
\item \highlight{团队领导}：担任\highlight{5人团队}项目负责人，协调开发进度和技术方案
\item \highlight{架构设计}：负责核心功能模块的系统架构设计和代码实现
\item \highlight{功能实现}：实现用户社交、音乐分享、评论互动等\highlight{社区核心功能}
\item \highlight{项目管理}：使用Git进行版本控制，确保项目\highlight{按时高质量交付}
\end{itemize}
}

% 技能专长
\section{技术技能}

% 编程语言技能条
\cvitem{编程语言}{
\iconCode \hspace{0.1cm} \skillbadge{C++} \textcolor{mainblue}{████████████} 熟练 \hspace{1cm}
\skillbadge{Python} \textcolor{mainblue}{████████████} 熟练 \hspace{1cm}
\skillbadge{JavaScript} \textcolor{mainblue}{████████░░░░} 良好
}

% 技术框架
\cvitem{技术框架}{
\iconSkill \hspace{0.1cm} \skillbadge{TensorFlow/PyTorch} \skillbadge{OpenCV} \skillbadge{Scikit-learn} \skillbadge{React} \skillbadge{Node.js}
}

% 专业领域
\cvitem{专业领域}{
\iconStar \hspace{0.1cm} \skillbadge{机器学习} \skillbadge{深度学习} \skillbadge{计算机视觉} \skillbadge{算法设计} \skillbadge{数据结构}
}

% 开发工具
\cvitem{开发工具}{
\iconSkill \hspace{0.1cm} \skillbadge{Git/GitHub} \skillbadge{VS Code} \skillbadge{Jupyter} \skillbadge{Docker} \skillbadge{Linux}
}

% 校园经历与实习
\section{校园经历}
\cventry{2025.07--至今}{\highlight{学干}}{计算机信息科学协会}{}{}{
\begin{itemize}
\item \iconSchool \hspace{0.1cm} \highlight{活动组织}：策划并组织\highlight{10+场}技术分享会和编程竞赛培训活动
\item \iconUser \hspace{0.1cm} \highlight{学术指导}：为\highlight{50+名}新生提供专业学习指导和竞赛培训
\item \iconStar \hspace{0.1cm} \highlight{团队协作}：与协会成员协作，提升组织的技术影响力
\end{itemize}
}

% 求职意向
\section{求职意向}
\cvitem{期望职位}{\iconTarget \hspace{0.1cm} \highlight{C++开发工程师} / \highlight{算法工程师} / \highlight{人工智能工程师}}
\cvitem{工作地点}{\iconSchool \hspace{0.1cm} 不限（可接受出差）}
\cvitem{到岗时间}{\iconCode \hspace{0.1cm} 2027年7月（可提前实习）}

% 个人评价
\section{个人评价}
\cvitem{}{
\iconStar \hspace{0.2cm}
\begin{itemize}
\item \highlight{技术热情}：对算法和AI技术有浓厚兴趣，持续学习新技术
\item \highlight{实践能力}：具备从理论到实践的完整项目开发经验
\item \highlight{团队合作}：良好的沟通能力和团队协作精神
\item \highlight{学习能力}：快速学习新技术，适应能力强
\item \highlight{责任心}：对工作认真负责，追求代码质量和项目效果
\end{itemize}
}

\end{CJK}
\end{document}
